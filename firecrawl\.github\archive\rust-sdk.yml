name: Run Rust SDK E2E Tests

on: []

env:
  ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
  BULL_AUTH_KEY: ${{ secrets.BULL_AUTH_KEY }}
  FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
  HOST: ${{ secrets.HOST }}
  LLAMAPARSE_API_KEY: ${{ secrets.LLAMAPARSE_API_KEY }}
  POSTHOG_API_KEY: ${{ secrets.POSTHOG_API_KEY }}
  POSTHOG_HOST: ${{ secrets.POSTHOG_HOST }}
  NUM_WORKERS_PER_QUEUE: ${{ secrets.NUM_WORKERS_PER_QUEUE }}
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
  PLAYWRIGHT_MICROSERVICE_URL: ${{ secrets.PLAYWRIGHT_MICROSERVICE_URL }}
  PORT: ${{ secrets.PORT }}
  REDIS_URL: ${{ secrets.REDIS_URL }}
  SUPABASE_ANON_TOKEN: ${{ secrets.SUPABASE_ANON_TOKEN }}
  SUPABASE_SERVICE_TOKEN: ${{ secrets.SUPABASE_SERVICE_TOKEN }}
  SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
  TEST_API_KEY: ${{ secrets.TEST_API_KEY }}
  HDX_NODE_BETA_MODE: 1


jobs:
  build:
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis
        ports:
          - 6379:6379

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Install pnpm
        run: npm install -g pnpm
      - name: Install dependencies for API
        run: pnpm install
        working-directory: ./apps/api
      - name: Start the application
        run: npm start &
        working-directory: ./apps/api
        id: start_app
      - name: Start workers
        run: npm run workers &
        working-directory: ./apps/api
        id: start_workers
      - name: Set up Rust
        uses: actions/setup-rust@v1
        with:
          rust-version: stable
      - name: Try the lib build
        working-directory: ./apps/rust-sdk
        run: cargo build
      - name: Run E2E tests for Rust SDK
        run: cargo test --test e2e_with_auth
