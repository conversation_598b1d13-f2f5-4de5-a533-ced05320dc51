---
name: Feature request
about: Suggest an idea for this project
title: "[Feat] "
labels: ''
assignees: ''

---

**Problem Description**
Describe the issue you're experiencing that has prompted this feature request. For example, "I find it difficult when..."

**Proposed Feature**
Provide a clear and concise description of the feature you would like implemented.

**Alternatives Considered**
Discuss any alternative solutions or features you've considered. Why were these alternatives not suitable?

**Implementation Suggestions**
If you have ideas on how the feature could be implemented, share them here. This could include technical details, API changes, or interaction mechanisms.

**Use Case**
Explain how this feature would be used and what benefits it would bring. Include specific examples to illustrate how this would improve functionality or user experience.

**Additional Context**
Add any other context such as comparisons with similar features in other products, or links to prototypes or mockups.
