# 📁 清理後的專案結構

```
firecrawl/
├── README.md                    # 快速開始指南
├── Firecrawl_使用指南.md        # 詳細使用指南
├── requirements.txt             # Python 依賴套件
├── firecrawl_manager.sh         # Linux/WSL 服務管理腳本
├── start_firecrawl.ps1          # Windows PowerShell 管理腳本
├── examples/                    # 使用範例
│   ├── basic_scraping.py        # 基本爬取範例
│   └── structured_extraction.py # 結構化數據提取範例
└── firecrawl/                   # Firecrawl 服務本體
    ├── docker-compose.yaml     # Docker 編排檔案
    ├── .env                     # 環境變數設定
    └── apps/                    # 應用程式目錄
```

## 🎯 核心檔案說明

- **README.md**: 快速開始和基本使用說明
- **Firecrawl_使用指南.md**: 詳細的功能說明和進階用法
- **requirements.txt**: Python 專案所需的套件
- **examples/**: 實用的程式範例，可直接修改使用
- **firecrawl/**: 完整的 Firecrawl 服務，包含 Docker 配置

## 🚀 如何在其他專案中使用

請參考新的 README.md 檔案中的「在其他專案中使用」章節。
