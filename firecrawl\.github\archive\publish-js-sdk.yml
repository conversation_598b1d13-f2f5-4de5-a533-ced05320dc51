name: Publish JavaScript SDK

on: []

env:
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  build-and-publish:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          registry-url: 'https://registry.npmjs.org/'
          scope: '@mendable'
          always-auth: true

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install python for running version check script
        run: |
          python -m pip install --upgrade pip
          pip install setuptools wheel requests packaging

      - name: Install dependencies for JavaScript SDK
        run: pnpm install
        working-directory: ./apps/js-sdk/firecrawl
        
      - name: Run version check script
        id: version_check_script
        run: |
          VERSION_INCREMENTED=$(python .github/scripts/check_version_has_incremented.py js ./apps/js-sdk/firecrawl @mendable/firecrawl-js)
          echo "VERSION_INCREMENTED=$VERSION_INCREMENTED" >> $GITHUB_ENV

      - name: Build and publish to npm
        if: ${{ env.VERSION_INCREMENTED == 'true' }}
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: |
          npm run build-and-publish
        working-directory: ./apps/js-sdk/firecrawl
