# 🚀 在其他專案中使用 Firecrawl

## 📋 快速整合步驟

### 1. 安裝依賴套件

```bash
# 安裝 Firecrawl Python SDK
pip install firecrawl-py

# 安裝其他常用套件
pip install pydantic requests python-dotenv
```

### 2. 選擇使用方式

#### 方式 A：使用本地服務（推薦開發環境）

**優點**：
- 免費使用
- 完全控制
- 無 API 限制
- 數據隱私

**前置條件**：
- 需要運行本地 Firecrawl 服務
- 需要 Docker 環境

#### 方式 B：使用雲端服務（推薦生產環境）

**優點**：
- 無需維護服務器
- 高可用性
- 自動擴展
- 專業支援

**前置條件**：
- 需要註冊 [firecrawl.dev](https://firecrawl.dev)
- 需要 API Key

## 🔧 基本設定

### 環境變數設定

建立 `.env` 檔案：

```env
# 選擇一種方式

# 方式 A：本地服務
FIRECRAWL_API_URL=http://localhost:3002

# 方式 B：雲端服務
# FIRECRAWL_API_KEY=fc-your_api_key_here

# 可選：OpenAI API Key（用於 AI 功能）
# OPENAI_API_KEY=your_openai_api_key_here
```

### 基本連接設定

```python
import os
from dotenv import load_dotenv
from firecrawl import FirecrawlApp

# 載入環境變數
load_dotenv()

def get_firecrawl_app():
    """取得 Firecrawl 應用實例"""
    api_key = os.getenv('FIRECRAWL_API_KEY')
    api_url = os.getenv('FIRECRAWL_API_URL')
    
    if api_key:
        # 使用雲端服務
        return FirecrawlApp(api_key=api_key)
    elif api_url:
        # 使用本地服務
        return FirecrawlApp(api_url=api_url)
    else:
        # 預設使用本地服務
        return FirecrawlApp(api_url="http://localhost:3002")

# 使用範例
app = get_firecrawl_app()
```

## 📚 實用範例

### 1. 基本網頁爬取

```python
def scrape_webpage(url):
    """爬取單個網頁"""
    app = get_firecrawl_app()
    
    try:
        result = app.scrape_url(
            url,
            formats=["markdown", "html"],
            onlyMainContent=True  # 只提取主要內容
        )
        
        return {
            'success': True,
            'title': result.get('metadata', {}).get('title'),
            'content': result.get('markdown'),
            'url': url
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'url': url
        }

# 使用範例
result = scrape_webpage("https://example.com")
if result['success']:
    print(f"標題: {result['title']}")
    print(f"內容: {result['content'][:200]}...")
```

### 2. 批量爬取

```python
def batch_scrape(urls):
    """批量爬取多個網頁"""
    results = []
    
    for url in urls:
        print(f"正在爬取: {url}")
        result = scrape_webpage(url)
        results.append(result)
        
        # 避免過於頻繁的請求
        import time
        time.sleep(1)
    
    return results

# 使用範例
urls = [
    "https://example.com",
    "https://httpbin.org/html",
    "https://quotes.toscrape.com"
]

results = batch_scrape(urls)
successful = [r for r in results if r['success']]
print(f"成功爬取 {len(successful)}/{len(urls)} 個網頁")
```

### 3. 結構化數據提取

```python
from pydantic import BaseModel
from typing import List, Optional

class Product(BaseModel):
    name: str
    price: Optional[str]
    description: Optional[str]
    rating: Optional[str]

class NewsArticle(BaseModel):
    title: str
    author: Optional[str]
    publish_date: Optional[str]
    summary: str
    tags: Optional[List[str]]

def extract_structured_data(url, schema_model):
    """提取結構化數據"""
    app = get_firecrawl_app()
    
    try:
        result = app.scrape_url(
            url,
            formats=["extract"],
            extract={"schema": schema_model.model_json_schema()}
        )
        
        return {
            'success': True,
            'data': result.get('extract'),
            'url': url
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'url': url
        }

# 使用範例
product_data = extract_structured_data(
    "https://shop.example.com/product/123",
    Product
)

if product_data['success']:
    product = Product(**product_data['data'])
    print(f"產品: {product.name}")
    print(f"價格: {product.price}")
```

### 4. 網站地圖和爬取

```python
def crawl_website_systematically(base_url, max_pages=10):
    """系統性爬取網站"""
    app = get_firecrawl_app()
    
    # 1. 先獲取網站地圖
    try:
        map_result = app.map_url(base_url)
        all_links = map_result.get('links', [])
        print(f"發現 {len(all_links)} 個連結")
    except Exception as e:
        print(f"獲取網站地圖失敗: {e}")
        return []
    
    # 2. 選擇要爬取的連結
    links_to_crawl = all_links[:max_pages]
    
    # 3. 批量爬取
    results = []
    for link in links_to_crawl:
        result = scrape_webpage(link)
        if result['success']:
            results.append(result)
    
    return results

# 使用範例
website_data = crawl_website_systematically("https://example.com", max_pages=5)
print(f"成功爬取 {len(website_data)} 個頁面")
```

## 🔧 進階配置

### 錯誤處理和重試

```python
import time
from functools import wraps

def retry_on_failure(max_retries=3, delay=1):
    """重試裝飾器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    print(f"嘗試 {attempt + 1} 失敗: {e}")
                    time.sleep(delay * (attempt + 1))
            return None
        return wrapper
    return decorator

@retry_on_failure(max_retries=3, delay=2)
def robust_scrape(url):
    """具有重試機制的爬取函數"""
    return scrape_webpage(url)
```

### 結果快取

```python
import json
import hashlib
from pathlib import Path

class FirecrawlCache:
    def __init__(self, cache_dir="firecrawl_cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    
    def _get_cache_key(self, url):
        """生成快取鍵"""
        return hashlib.md5(url.encode()).hexdigest()
    
    def get(self, url):
        """從快取獲取結果"""
        cache_file = self.cache_dir / f"{self._get_cache_key(url)}.json"
        if cache_file.exists():
            with open(cache_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    
    def set(self, url, data):
        """儲存結果到快取"""
        cache_file = self.cache_dir / f"{self._get_cache_key(url)}.json"
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

# 使用快取的爬取函數
cache = FirecrawlCache()

def cached_scrape(url):
    """帶快取的爬取函數"""
    # 先檢查快取
    cached_result = cache.get(url)
    if cached_result:
        print(f"從快取獲取: {url}")
        return cached_result
    
    # 爬取新數據
    result = scrape_webpage(url)
    if result['success']:
        cache.set(url, result)
    
    return result
```

## 🎯 專案模板

建立一個新的 Python 專案時，可以使用以下結構：

```
my_project/
├── .env                    # 環境變數
├── requirements.txt        # 依賴套件
├── config.py              # 配置設定
├── scraper.py             # 爬取邏輯
├── models.py              # 數據模型
├── utils.py               # 工具函數
└── main.py                # 主程式
```

### requirements.txt
```
firecrawl-py>=1.0.0
pydantic>=2.0.0
python-dotenv>=1.0.0
requests>=2.31.0
```

### config.py
```python
import os
from dotenv import load_dotenv

load_dotenv()

FIRECRAWL_API_KEY = os.getenv('FIRECRAWL_API_KEY')
FIRECRAWL_API_URL = os.getenv('FIRECRAWL_API_URL', 'http://localhost:3002')
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
```

## 🚀 開始你的專案

1. **複製範例檔案**：將 `examples/` 目錄複製到你的新專案
2. **安裝依賴**：`pip install -r requirements.txt`
3. **設定環境變數**：建立 `.env` 檔案
4. **修改範例**：根據你的需求修改範例程式
5. **開始開發**：使用 Firecrawl 建立你的爬蟲應用

## 💡 最佳實踐

1. **尊重網站政策**：遵守 robots.txt 和使用條款
2. **合理控制頻率**：避免過於頻繁的請求
3. **錯誤處理**：實作重試機制和錯誤處理
4. **數據驗證**：使用 Pydantic 驗證提取的數據
5. **快取機制**：避免重複爬取相同內容

祝你使用 Firecrawl 開發順利！🎉
