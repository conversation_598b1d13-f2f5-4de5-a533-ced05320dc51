{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Comprehensive GitHub Actions Tutorial For Beginners With Examples in Python"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["GitHub Actions is a powerful automation platform that helps developers automate tedious, time-wasting software development workflows. Instead of running tests, executing scripts at intervals, or doing any programmable task manually, you can let GitHub Actions take the wheel when certain events happen in your repository. In this tutorial, you will learn how to use this critical feature of GitHub and design your own workflows for several real-world use cases."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### What are GitHub Actions?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["At its core, [GitHub Actions](https://docs.github.com/en/actions) is a continuous integration and continuous delivery (CI/CD) platform that lets you automate various tasks directly from your GitHub repository. Think of it as your personal robot assistant that can:\n", "\n", "- Run your Python tests automatically when you push code\n", "- Deploy your application when you create a new release\n", "- Send notifications when issues are created\n", "- Schedule tasks to run at specific times\n", "- And much more..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Why automate with GitHub Actions?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's look at a common scenario: You are building a Python application that scrapes product prices from various e-commerce websites. Without GitHub actions, you would need to:\n", "\n", "1. Manually run your tests after each code change\n", "2. Remember to execute the scraper at regular intervals\n", "3. Deploy updates to your production environment\n", "4. Keep track of environment variables and secrets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "With GitHub actions, all of these tasks can be automated through workflows, usually written in YAML files like below:\n", "\n", "```yaml\n", "name: <PERSON>\n", "\n", "on:\n", "  schedule:\n", "    - cron: '0 */12 * * *'  # Runs every 12 hours\n", "  workflow_dispatch:  # Allows manual triggers\n", "\n", "jobs:\n", "  scrape:\n", "    runs-on: ubuntu-latest\n", "    \n", "    steps:\n", "    - uses: actions/checkout@v3\n", "    - name: Set up Python\n", "      uses: actions/setup-python@v4\n", "      with:\n", "        python-version: '3.9'\n", "        \n", "    - name: <PERSON> scraper\n", "      env:\n", "        API_KEY: ${{ secrets.FIRECRAWL_API_KEY }}\n", "      run: python scraper.py\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This workflow automatically runs a scraper every 12 hours, handles Python version setup, and securely manages API keys - all without manual intervention."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### What we'll build in this tutorial"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Throughout this tutorial, we'll build several practical GitHub Actions workflows for Python applications. You will learn how to:\n", "\n", "1. Create basic and advanced workflow configurations.\n", "2. Work with environment variables and secrets.\n", "3. Set up automated testing pipelines.\n", "4. Build a real-world example: an automated scraping system app [Firecrawl](https://firecrawl.dev) in Python.\n", "5. Implement best practices for security and efficiency. \n", "\n", "By the end, you will have hands-on experience with GitHub Actions and be able to automate your own Python projects effectively. \n", "\n", "> Note: Even though code examples are Python, the concepts and hands-on experience you will gain from the tutorial will apply to any programming language. \n", "\n", "Let's start by understanding the core concepts that make GitHub Actions work."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Understanding GitHub Actions Core Concepts"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To write your own GitHub Actions workflows, you need to understand how its different components work together. Let's break down these core concepts using a practical example: automating tests for a simple Python script."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### GitHub Actions workflows and their components"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A workflow is an automated process that you define in a YAML file within your repository's `.github/workflows` directory. Think of it as a recipe that tells GitHub exactly what to do, how and when to do it. You can transform virtually any programmable task into a GitHub workflow as long as it can be executed in a Linux, Windows, or macOS environment and doesn't require direct user interaction.\n", "\n", "Here is a basic workflow structure:\n", "\n", "```yaml\n", "# test.yaml\n", "name: Python Tests\n", "on: [push, pull_request]\n", "\n", "jobs:\n", "  test:\n", "    runs-on: ubuntu-latest\n", "    steps:\n", "      - name: Check out repository\n", "        uses: actions/checkout@v3\n", "      \n", "      - name: Set up Python\n", "        uses: actions/setup-python@v4\n", "        with:\n", "          python-version: '3.9'\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The YAML file starts by specifying the name of the workflow with the `name` field. Immediately after, we specify the events that triggers this workflow. In this example, the workflow automatically executes on each `git push` command and pull request. We will learn more about events and triggers in a later section. \n", "\n", "Next, we define jobs, which are the building blocks of workflows. Each job:\n", "\n", "- Runs on a fresh virtual machine (called a runner) that is specified using the `runs-on` field.\n", "- Can execute multiple steps in sequence\n", "- Can run in parallel with other jobs\n", "- Has access to shared workflow data\n", "\n", "For example, you might have separate jobs for testing and deployment:\n", "\n", "```yaml\n", "jobs:\n", "    test:\n", "        runs-on: ubuntu-latest\n", "        ...\n", "    deploy:\n", "        runs-on: macos-latest\n", "        ...\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Each job can contain one or more `steps` that are executed sequentially. Steps are individual tasks that make up your job. They can:\n", "\n", "- Run commands or shell scripts\n", "- Execute actions (reusable units of code)\n", "- Run commands in Docker containers\n", "- Reference other GitHub repositories\n", "\n", "For example, a typical test job might have steps to:\n", "\n", "1. Check out (clone) code from your GitHub repository\n", "2. Set up dependencies\n", "3. Run tests\n", "4. Upload test results\n", "\n", "Each step can specify:\n", "\n", "- `name`: A display name for the step\n", "- `uses`: Reference to an action to run\n", "- `run`: Any operating-system specific terminal command like `pip install package` or `python script.py`\n", "- `with`: Input parameters for actions\n", "- `env`: Environment variables for the step"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we understand jobs and steps, let's look at Actions - the reusable building blocks that make GitHub Actions so powerful.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Actions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `test.yaml` file from earlier has a single `test` job that executes two steps:\n", "\n", "1. Checking out the repository code using a built-in `actions/checkout@v3` action.\n", "2. Setting up a Python environment with `actions/setup-python@v4` and `python-version` as an input parameter for said action.\n", "\n", "```bash\n", "# test.yaml\n", "name: Python Tests\n", "on: [push, pull_request]\n", "\n", "jobs:\n", "  test:\n", "    runs-on: ubuntu-latest\n", "    steps:\n", "      - name: Check out repository\n", "        uses: actions/checkout@v3\n", "      \n", "      - name: Set up Python\n", "        uses: actions/setup-python@v4\n", "        with:\n", "          python-version: '3.9'\n", "```\n", "\n", "Actions are reusable units of code that can be shared across workflows (this is where GitHub Actions take its name). They are like pre-packaged functions that handle common tasks. For instance, instead of writing code to set up Node.js or caching dependencies, you can use the GitHub official actions like:\n", "\n", "- `actions/setup-node@v3` - Sets up Node.js environment\n", "- `actions/cache@v3` - Caches dependencies and build outputs\n", "- `actions/upload-artifact@v3` - Uploads workflow artifacts\n", "- `actions/download-artifact@v3` - Downloads workflow artifacts\n", "- `actions/labeler@v4` - Automatically labels pull requests\n", "- `actions/stale@v8` - Marks and closes stale issues/PRs\n", "- `actions/dependency-review-action@v3` - Reviews dependency changes\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Events and triggers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Events are specific activities that trigger a workflow. Common triggers include:\n", "\n", "- `push`: When code is pushed to the repository\n", "- `pull_request`: When a PR is opened or updated\n", "- `schedule`: At specified times using cron syntax\n", "- `workflow_dispatch`: Manual trigger via GitHub UI\n", "\n", "Here is how you can configure multiple triggers:\n", "\n", "```yaml\n", "name: Comprehensive Workflow\n", "on:\n", "  push:\n", "    branches: [main]\n", "  pull_request:\n", "    branches: [main]\n", "  schedule:\n", "    - cron: '0 0 * * *'  # Daily at midnight\n", "  workflow_dispatch:  # Manual trigger\n", "\n", "jobs:\n", "  process:\n", "    runs-on: ubuntu-latest\n", "    steps:\n", "      - uses: actions/checkout@v3\n", "      - name: Run daily tasks\n", "        run: python daily_tasks.py\n", "        env:\n", "          API_KEY: ${{ secrets.FIRECRAWL_API_KEY }}\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This example shows how a single workflow can:\n", "\n", "- Run automatically on code changes on `git push`\n", "- Execute daily scheduled tasks with cron\n", "- Be triggered automatically when needed through the GitHub UI\n", "- Handle sensitive data like API keys securely"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cron jobs in GitHub Actions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To use the `schedule` trigger effectively in GitHub Actions, you'll need to understand cron syntax. This powerful scheduling format lets you automate workflows to run at precise times. The syntax uses five fields to specify when a job should run:\n", "\n", "![](images/cron-syntax.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here are some common cron schedule examples:\n", "\n", "```yaml\n", "# Daily at 3:30 AM UTC\n", "- cron: '30 3 * * *'\n", "\n", "# Every Monday at 1:00 PM UTC\n", "- cron: '0 13 * * 1'\n", "\n", "# Every 6 hours at the first minute\n", "- cron: '0 */6 * * *'\n", "\n", "# At minute 15 of every hour\n", "- cron: '15 * * * *'\n", "\n", "# Every weekday (Monday through Friday)\n", "- cron: '0 0 * * 1-5'\n", "\n", "# Each day at 12am, 6am, 12pm, 6pm on Tuesday, Thursday, Saturday\n", "- cron: '0 0,6,12,18 * * 1,3,5'\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here is a sample workflow for a scraping job with four different schedules (multiple schedules are allowed):\n", "\n", "```yaml\n", "name: Price Scraper Schedules\n", "on:\n", "  schedule:\n", "    - cron: '0 */4 * * *'    # Every 4 hours\n", "    - cron: '30 1 * * *'     # Daily at 1:30 AM UTC\n", "    - cron: '0 9 * * 1-5'    # Weekdays at 9 AM UTC\n", "\n", "jobs:\n", "  scrape:\n", "    runs-on: ubuntu-latest\n", "    steps:\n", "      - uses: actions/checkout@v3\n", "      - name: <PERSON>crawl scraper\n", "        env:\n", "          API_KEY: ${{ secrets.FIRECRAWL_API_KEY }}\n", "        run: python scraper.py\n", "```\n", "\n", "Remember that GitHub Actions runs on UTC time, and schedules might experience slight delays during peak GitHub usage. That's why it's helpful to combine `schedule` with `workflow_dispatch` as we saw earlier - giving you both automated and manual trigger options."]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "---------------\n", "\n", "Understanding these core concepts allows you to create workflows that are efficient (running only when needed), secure (properly handling sensitive data), maintainable (using reusable actions) and scalable (running on different platforms). \n", "\n", "In the next section, we will put these concepts into practice by creating your first GitHub actions workflow."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Your First GitHub Actions Workflow"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's create a practical GitHub Actions workflow from scratch. We'll build a workflow that automatically tests a Python script and runts it on a schedule - a universal task applicable to any programming language. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting up the environment"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's start by creating a working directory for this mini-project:\n", "\n", "```bash\n", "mkdir first-workflows\n", "cd first-workflows\n", "```\n", "\n", "Let's create the standard `.github/workflows` folder structure GitHub uses for detecting workflow files:\n", "\n", "```bash\n", "mkdir -p .github/workflows\n", "```\n", "\n", "The workflow files can have any name but must have a `.yml` extension:\n", "\n", "```bash\n", "touch .github/workflows/system_monitor.yml\n", "```\n", "\n", "In addition to the workflows folder, create a `tests` folder as well as a test file:\n", "\n", "```bash\n", "mkdir tests\n", "touch tests/test_main.py\n", "```\n", "\n", "We should also create the `main.py` file along with a `requirements.txt`:\n", "\n", "```bash\n", "touch main.py requirements.txt\n", "```\n", "\n", "Then, add these two dependencies to `requirements.txt`:\n", "\n", "```text\n", "psutil>=5.9.0\n", "pytest>=7.0.0\n", "```\n", "\n", "Finally, let's initialize git and make our first commit:\n", "\n", "```bash\n", "git init \n", "git add .\n", "git commit -m \"Initial commit\"\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Check out the [Git documentation](https://git-scm.com/doc) if you don't have it installed already."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Writing your first workflow file"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's write the workflow logic first. Open `system_monitor.yml` and paste each code snippet we are about to define one after the other. \n", "\n", "1. Workflow name and triggers:\n", "\n", "```yaml\n", "name: System Monitoring\n", "on:\n", "  schedule:\n", "    - cron: '*/30 * * * *'  # Run every 30 minutes\n", "  workflow_dispatch:        # Enables manual trigger\n", "```\n", "\n", "In this part, we give a descriptive name to the workflow that appears in GitHub's UI. Using the `on` field, we set the workflow to run every 30 minutes and through a manual trigger."]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "2. Job definition:\n", "\n", "```yaml\n", "jobs:\n", "  run_script:\n", "    runs-on: ubuntu-latest\n", "```\n", "\n", "`jobs` contains all the jobs in this workflow and it has a `run_script` name, which is a unique identifier. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["3. Steps:\n", "\n", "There are five steps that run sequentially in this workflow. They are given descriptive names that appear in the GitHub UI and uses official GitHub actions and custom terminal commands. \n", "\n", "```yaml\n", "jobs:\n", "  monitor:\n", "    runs-on: ubuntu-latest\n", "    \n", "    steps:\n", "      - name: Check out repository\n", "        uses: actions/checkout@v3\n", "      \n", "      - name: Set up Python\n", "        uses: actions/setup-python@v4\n", "        with:\n", "          python-version: '3.9'\n", "      \n", "      - name: Install dependencies\n", "        run: |\n", "          python -m pip install --upgrade pip\n", "          pip install -r requirements.txt\n", "      \n", "      - name: Run tests\n", "        run: pytest tests/\n", "      \n", "      - name: Collect system metrics\n", "        run: python main.py\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here is what each step does:\n", "\n", "1. Check out repository code with `actions/checkout@v3`.\n", "2. Configures Python 3.9 environment.\n", "3. Runs two terminal commands that:\n", "    - Install/upgrade `pip`\n", "    - Install `pytest` package\n", "4. Runs the tests located in the `tests` directory using `pytest`.\n", "5. Executes the main script with `python main.py`. \n", "\n", "Notice the use of `|` (pipe) operator for multi-line commands.\n", "\n", "After you complete writing the workflow, commit the changes to Git:\n", "\n", "```bash\n", "git add .\n", "git commit -m \"Add a workflow file for monitoring system resources\"\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating the Python script"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's write the `main.py` file, which is a monitoring script that helps software developers track system resource usage over time, enabling them to identify performance bottlenecks and capacity issues in their development environment. \n", "\n", "```python\n", "import psutil\n", "import json\n", "from datetime import datetime\n", "from pathlib import Path\n", "```\n", "\n", "This script collects and logs system metrics over time. It uses `psutil` to gather CPU usage, memory usage, disk usage, and active process counts. The metrics are timestamped and saved to JSON files organized by date.\n", "\n", "The script has three main functions:\n", "\n", "```python\n", "def get_system_metrics():\n", "    \"\"\"Collect key system metrics\"\"\"\n", "    metrics = {\n", "        \"cpu_percent\": psutil.cpu_percent(interval=1),\n", "        \"memory_percent\": psutil.virtual_memory().percent,\n", "        \"disk_usage\": psutil.disk_usage('/').percent,\n", "        \"timestamp\": datetime.now().isoformat()\n", "    }\n", "    \n", "    # Add running processes count\n", "    metrics[\"active_processes\"] = len(psutil.pids())\n", "    \n", "    return metrics\n", "```\n", "\n", "`get_system_metrics()` - Collects current system metrics including CPU percentage, memory usage percentage, disk usage percentage, timestamp, and count of active processes.\n", "\n", "```python\n", "def save_metrics(metrics):\n", "    \"\"\"Save metrics to a JSON file with today's date\"\"\"\n", "    date_str = datetime.now().strftime(\"%Y-%m-%d\")\n", "    reports_dir = Path(\"system_metrics\")\n", "    reports_dir.mkdir(exist_ok=True)\n", "    \n", "    # Save to daily file\n", "    file_path = reports_dir / f\"metrics_{date_str}.json\"\n", "    \n", "    # Load existing metrics if file exists\n", "    if file_path.exists():\n", "        with open(file_path) as f:\n", "            daily_metrics = json.load(f)\n", "    else:\n", "        daily_metrics = []\n", "    \n", "    # Append new metrics\n", "    daily_metrics.append(metrics)\n", "    \n", "    # Save updated metrics\n", "    with open(file_path, 'w') as f:\n", "        json.dump(daily_metrics, f, indent=2)\n", "```\n", "\n", "`save_metrics()` - <PERSON>les saving the metrics to JSON files. It creates a `system_metrics` directory if needed, and saves metrics to date-specific files (e.g. `metrics_2024-12-12.json`). If a file for the current date exists, it loads and appends to it, otherwise creates a new file.\n", "\n", "```python\n", "def main():\n", "    try:\n", "        metrics = get_system_metrics()\n", "        save_metrics(metrics)\n", "        print(f\"System metrics collected at {metrics['timestamp']}\")\n", "        print(f\"CPU: {metrics['cpu_percent']}% | Memory: {metrics['memory_percent']}%\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Error collecting metrics: {str(e)}\")\n", "        return False\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "```\n", "\n", "`main()` - Orchestrates the metric collection and saving process. It calls `get_system_metrics()`, saves the data via `save_metrics()`, prints current CPU and memory usage to console, and handles any errors that occur during execution.\n", "\n", "The script can be run directly or imported as a module. When run directly (which is what happens in a GitHub Actions workflow), it executes the `main()` function which collects and saves one set of metrics."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Combine the code snippets above into the `main.py` file and commit the changes:\n", "\n", "```bash\n", "git add .\n", "git commit -m \"Add the main.py functionality\"\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding tests"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Testing is a critical part of software engineering workflows for several reasons:\n", "\n", "1. Reliability: Tests help ensure code behaves correctly and consistently across changes.\n", "2. Regression prevention: Tests catch when new changes break existing functionality.\n", "3. Documentation: Tests serve as executable documentation of expected behavior\n", "4. Design feedback: Writing tests helps identify design issues early\n", "5. Confidence: A good test suite gives confidence when refactoring or adding features\n", "\n", "For our system metrics collection script, tests would be valuable to verify:\n", "\n", "- The `get_system_metrics()` function returns data in the expected format with valid ranges\n", "- The `save_metrics()` function properly handles file operations and JSON serialization\n", "- Error handling works correctly for various failure scenarios\n", "- The `main()` function orchestrates the workflow as intended\n", "\n", "With that said, let's work on the `tests/test_main.py` file:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```python\n", "import json\n", "from datetime import datetime\n", "from pathlib import Path\n", "from main import get_system_metrics, save_metrics\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The test file we are about to write demonstrates key principles of testing with `pytest`, a popular Python testing framework. Pytest makes it easy to write tests by using simple `assert` statements and providing a rich set of features for test organization and execution. The test functions are automatically discovered by `pytest` when their names start with `test_`, and each function tests a specific aspect of the system's functionality.\n", "\n", "```python\n", "def test_get_system_metrics():\n", "    \"\"\"Test if system metrics are collected correctly\"\"\"\n", "    metrics = get_system_metrics()\n", "    \n", "    # Check if all required metrics exist and are valid\n", "    assert 0 <= metrics['cpu_percent'] <= 100\n", "    assert 0 <= metrics['memory_percent'] <= 100\n", "    assert metrics['active_processes'] > 0\n", "    assert 'timestamp' in metrics\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this example, we have two test functions that verify different components of our metrics collection system. The first test, `test_get_system_metrics()`, checks if the metrics collection function returns data in the expected format and with valid ranges. It uses multiple assert statements to verify that CPU and memory percentages are between 0 and 100, that there are active processes, and that a timestamp is included. This demonstrates the practice of testing both the structure of returned data and the validity of its values.\n", "\n", "```python\n", "def test_save_and_read_metrics():\n", "    \"\"\"Test if metrics are saved and can be read back\"\"\"\n", "    # Get and save metrics\n", "    metrics = get_system_metrics()\n", "    save_metrics(metrics)\n", "    \n", "    # Check if file exists and contains data\n", "    date_str = datetime.now().strftime(\"%Y-%m-%d\")\n", "    file_path = Path(\"system_metrics\") / f\"metrics_{date_str}.json\"\n", "    \n", "    assert file_path.exists()\n", "    with open(file_path) as f:\n", "        saved_data = json.load(f)\n", "    \n", "    assert isinstance(saved_data, list)\n", "    assert len(saved_data) > 0\n", "```\n", "\n", "The second test, `test_save_and_read_metrics()`, showcases integration testing by verifying that metrics can be both saved to and read from a file. It follows a common testing pattern: arrange (setup the test conditions), act (perform the operations being tested), and assert (verify the results). The test ensures that the file is created in the expected location and that the saved data maintains the correct structure. This type of test is particularly valuable as it verifies that different components of the system work together correctly.\n", "\n", "Combine the above code snippets and commit the changes to GitHub:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```bash\n", "git add .\n", "git commit -m \"Write tests for main.py\"\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running your first GitHub Actions workflow"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we've created our system monitoring workflow, let's set it up on GitHub and run it. First, push everything we have to a new GitHub repository:\n", "\n", "```bash\n", "git remote add origin https://github.com/your-username/your-repository.git\n", "git branch -M main\n", "git push -u origin main\n", "```\n", "\n", "Once the workflow file is pushed, GitHub automatically detects it and displays in the \"Actions\" tab of your repository. The workflow is scheduled so you don't need to do anything - the first workflow run will happen within 30 minutes (remember how we set the running interval to `*/30` with cron). Since the workflow also includes a `workflow_dispatch` field, you can trigger it manually by clicking on the \"Run workflow\" button. \n", "\n", "After clicking the button, a new run appears within a few seconds (refresh if you don't see it). To see the workflow run in real-time, click on it and expand the `monitor` job. You'll see each step executing:\n", "\n", "- Checking out repository\n", "- Setting up Python\n", "- Installing dependencies\n", "- Running tests\n", "- Collecting metrics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Committing changes made by GitHub Actions workflows"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Right now, our workflow file has a problem - while it successfully collects metrics, it doesn't commit and push the changes back to the repository. This means that although metrics are being gathered, they aren't being saved in version control. Let's modify the workflow to automatically commit and push the collected metrics:\n", "\n", "```yaml\n", "name: System Monitor\n", "\n", "on:\n", "  schedule:\n", "    - cron: '*/30 * * * *'\n", "  workflow_dispatch:\n", "\n", "permissions:\n", "  contents: write\n", "\n", "jobs:\n", "  monitor:\n", "    runs-on: ubuntu-latest\n", "    steps:\n", "      - uses: actions/checkout@v3\n", "      \n", "      - name: Set up Python\n", "        uses: actions/setup-python@v4\n", "        with:\n", "          python-version: '3.10'\n", "          \n", "      - name: Install dependencies\n", "        run: |\n", "          python -m pip install --upgrade pip\n", "          pip install -r requirements.txt\n", "          \n", "      - name: Run tests\n", "        run: python -m pytest\n", "        \n", "      - name: Collect metrics\n", "        run: python main.py\n", "        \n", "      - name: Commit and push changes\n", "        run: |\n", "          git config --global user.name 'github-actions[bot]'\n", "          git config --global user.email 'github-actions[bot]@users.noreply.github.com'\n", "          git add metrics.json\n", "          git commit -m \"Update metrics\" || exit 0\n", "          git push\n", "```\n", "\n", "The key changes in this updated workflow are:\n", "\n", "1. Added `permissions` block with `contents: write` to allow the workflow to push changes back to the repository.\n", "2. Added a new \"Commit and push changes\" step that:\n", "   - Configures git user identity as `github-actions` bot\n", "   - Stages the `metrics.json` file\n", "   - Creates a commit with message \"Update metrics\" \n", "   - Pushes the changes back to the repository\n", "   \n", "The \"|| exit 0\" after `git commit` ensures the workflow doesn't fail if there are no changes to commit.\n", "\n", "This allows the workflow to automatically save and version control the metrics it collects. Let's commit the changes to the workflow and push:\n", "\n", "```bash\n", "git add .\n", "git commit -m \"Add a commit step to the workflow file\"\n", "git push origin main\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After this change, try running the workflow manually and verify its success by navigating to the Actions tab in your GitHub repository. You should see the workflow run and the `metrics.json` file updated with new data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Managing Sensitive Data and Environment Variables"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When building automated workflows with GitHub Actions, proper handling of sensitive data like API keys, passwords, and access tokens is crucial for security. Let's explore best practices for managing these credentials."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Understanding environment variables in GitHub Actions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Environment variables in GitHub Actions can be set at different levels:\n", "\n", "- Repository level (GitHub Secrets)\n", "- Workflow level\n", "- Job level\n", "- Step level\n", "\n", "Here is how to properly configure and use them:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1. Setting up repository secrets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First, store sensitive values as repository secrets:\n", "\n", "1. Navigate to your GitHub repository\n", "2. Go to Settings → Secrets and variables → Actions\n", "3. <PERSON><PERSON> \"New repository secret\"\n", "4. Add your secrets with descriptive names like:\n", "\n", "- `API_KEY`\n", "- `DATABASE_URL`\n", "- `AWS_ACCESS_KEY`\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2. Using secrets in workflows"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Reference secrets in your workflow file using the `secrets` context:\n", "\n", "```yaml\n", "name: Web Scraping Pipeline\n", "# ... the rest of the file\n", "\n", "jobs:\n", "  scrape:\n", "    runs-on: ubuntu-latest\n", "    \n", "    steps:\n", "      # ... the rest of the steps\n", "          \n", "      - name: <PERSON> scraper\n", "        env:\n", "          API_KEY: ${{ secrets.API_KEY }}\n", "          DATABASE_URL: ${{ secrets.DATABASE_URL }}\n", "        run: python scraper.py\n", "```\n", "\n", "Above, the \"Run scraper\" step executes `scraper.py` which relies on two environment variables configured through the `secrets` context. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3. Local development with .env files"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For local development, use `.env` files to manage environment variables:\n", "\n", "```bash\n", "touch .env\n", "echo \"API_KEY='your-api-key-here'\" >> .env\n", "echo \"DATABASE_URL='postgresql://user:pass@localhost:5432/db'\" >> .env\n", "```\n", "\n", "Create a `.gitignore` file to prevent committing sensitive data:\n", "\n", "```bash\n", "echo \".env\" >> .giti<PERSON>re\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4. Loading environment variables in Python"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Use `python-dotenv` to load variables from `.env` files:\n", "\n", "```python\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "# Load environment variables from .env file\n", "load_dotenv()\n", "\n", "# Access variables\n", "api_key = os.getenv('API_KEY')\n", "database_url = os.getenv('DATABASE_URL')\n", "\n", "if not api_key or not database_url:\n", "    raise ValueError(\"Missing required environment variables\")\n", "```\n", "\n", "This code demonstrates loading environment variables from a `.env` file using `python-dotenv`. The `load_dotenv()` function reads the variables, which can then be accessed via `os.getenv()`. Basic validation ensures required variables exist."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 5. Environment variable validation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create a configuration class to validate environment variables:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```python\n", "from pydantic import BaseSettings, SecretStr\n", "\n", "\n", "class Settings(BaseSettings):\n", "    api_key: SecretStr\n", "    database_url: str\n", "    debug_mode: bool = False\n", "\n", "    class Config:\n", "        env_file = \".env\"\n", "        env_file_encoding = \"utf-8\"\n", "\n", "\n", "# Initialize settings\n", "settings = Settings()\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This approach using Pydantic provides several advantages for environment variable management:\n", "\n", "1. Type validation - Pydantic automatically validates types and converts values\n", "2. Default values - The `debug_mode` demonstrates setting defaults\n", "3. Secret handling - `SecretStr` provides secure handling of sensitive values\n", "4. Centralized config - All environment variables are defined in one place\n", "5. IDE support - Get autocomplete and type hints when using the settings object\n", "\n", "The `Settings` class inherits from `BaseSettings` which automatically loads from environment variables. The `Config` class specifies to also load from a `.env` file.\n", "\n", "Using `settings = Settings()` creates a validated configuration object that can be imported and used throughout the application. This is more robust than accessing `os.environ` directly.\n", "\n", "Example usage:\n", "\n", "```python\n", "settings.api_key.get_secret_value()  # Securely access API key\n", "settings.database_url  # Type-checked database URL\n", "settings.debug_mode  # Boolean with default value\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 6. Handle different environments"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Handle different environments (development, staging, production) using environment-specific files:\n", "\n", "```bash\n", ".env                # Default environment variables\n", ".env.development   # Development-specific variables\n", ".env.staging       # Staging-specific variables\n", ".env.production    # Production-specific variables\n", "```\n", "\n", "Load the appropriate file based on the environment:\n", "\n", "```bash\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "env = os.getenv('ENVIRONMENT', 'development')\n", "env_file = f'.env.{env}'\n", "\n", "load_dotenv(env_file)\n", "```\n", "\n", "This approach allows you to maintain separate configurations for different environments while keeping sensitive information secure. The environment-specific files can contain different values for the same variables, such as:\n", "\n", "- Development environment may use local services and dummy credentials\n", "- Staging environment may use test services with restricted access\n", "- Production environment contains real credentials and production service endpoints\n", "\n", "You can also combine this with the `Pydantic` settings approach shown earlier for robust configuration management across environments.\n", "\n", "For example, staging might use a test database while production uses the live database:\n", "\n", "```bash\n", "# .env.staging:\n", "DATABASE_URL=postgresql://test-db.example.com\n", "API_KEY=test-key\n", "```\n", "\n", "```bash\n", ".env.production:\n", "DATABASE_URL=postgresql://prod-db.example.com \n", "API_KEY=live-key\n", "```\n", "\n", "This separation helps prevent accidental use of production resources during development and testing."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building Real-World Python Workflows"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's explore three practical examples of GitHub Actions workflows for common Python tasks: web scraping, package publishing, and container builds."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. Scheduled web scraping with Firecrawl"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Web scraping is a common use case for automated workflows. Let's build a workflow that scrapes [Hacker News](https://news.ycombinator.com/) on a schedule using [Firecrawl](https://docs.firecrawl.dev), which is a Python AI-based web scraping engine designed for large-scale data collection. Here are some key benefits that make Firecrawl an excellent choice for this task:\n", "\n", "1. **Enterprise-grade automation and scalability** - Firecrawl streamlines web scraping with powerful automation features.\n", "2. **AI-powered content extraction** - Maintains scraper reliability over time by identifying and extracting data based on semantic descriptions instead of relying HTML elements and CSS selectors.\n", "3. **Handles complex scraping challenges** - Automatically manages proxies, anti-bot mechanisms, and dynamic JavaScript content.\n", "4. **Multiple output formats** - Supports scraping and converting data in markdown, tabular, screenshots, and HTML, making it versatile for various applications.\n", "5. **Built-in rate limiting and request management** - Ensures efficient and compliant data extraction.\n", "6. **Geographic location customization** - Avoids IP bans by customizing the geographic location of requests."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's build our web scraping workflow using Firecrawl to demonstrate these capabilities.\n", "\n", "```bash\n", "# Create project directory and install dependencies\n", "mkdir hacker-news-scraper && cd hacker-news-scraper\n", "pip install firecrawl-py pydantic python-dotenv\n", "\n", "# Create necessary files\n", "touch requirements.txt scraper.py .env\n", "\n", "# Add dependencies to requirements.txt\n", "echo \"firecrawl-py\\npydantic\\npython-dotenv\" > requirements.txt\n", "\n", "# Add Firecrawl API key to .env (get your key at firecrawl.dev/signin/signup)\n", "echo \"FIRECRAWL_API_KEY='your_api_key_here'\" > .env\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Open the scraper script where we define our scraping logic:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# scraper.py\n", "import json\n", "from firecrawl import FirecrawlApp\n", "from dotenv import load_dotenv\n", "from pydantic import BaseModel, Field\n", "from typing import List\n", "from datetime import datetime\n", "\n", "load_dotenv()\n", "\n", "BASE_URL = \"https://news.ycombinator.com/\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["First, we import necessary libraries and packages, also defining a base URL we are going to scrape."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class NewsItem(BaseModel):\n", "    title: str = Field(description=\"The title of the news item\")\n", "    source_url: str = Field(description=\"The URL of the news item\")\n", "    author: str = Field(\n", "        description=\"The URL of the post author's profile concatenated with the base URL.\"\n", "    )\n", "    rank: str = Field(description=\"The rank of the news item\")\n", "    upvotes: str = Field(description=\"The number of upvotes of the news item\")\n", "    date: str = Field(description=\"The date of the news item.\")\n", "\n", "\n", "class NewsData(BaseModel):\n", "    news_items: List[NewsItem]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We define two Pydantic models to structure our scraped data:\n", "\n", "1. `NewsItem` - Represents a single news item with fields for title, URL, author, rank, upvotes and date\n", "2. `NewsData` - Contains a list of `NewsItem` objects\n", "\n", "These models help validate the scraped data and ensure it matches our expected schema. They also make it easier to serialize/deserialize the data when saving to JSON. Using `Field` with a detailed description is crucial because Firecrawl uses these definitions to automatically detect the HTMl elements and CSS selectors we are looking for.\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def get_news_data():\n", "    app = FirecrawlApp()\n", "\n", "    data = app.scrape_url(\n", "        BASE_URL,\n", "        params={\n", "            \"formats\": [\"extract\"],\n", "            \"extract\": {\"schema\": NewsData.model_json_schema()},\n", "        },\n", "    )\n", "\n", "    return data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `get_news_data()` function uses Firecrawl to scrape Hacker News. It creates a `FirecrawlApp` instance and calls `scrape_url()` with the `BASE_URL` and parameters specifying we want to extract data according to our `NewsData` schema. The schema helps Firecrawl automatically identify and extract the relevant HTML elements. The function returns the scraped data containing news items with their titles, URLs, authors, ranks, upvotes and dates."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def save_firecrawl_news_data():\n", "    \"\"\"\n", "    Save the scraped news data to a JSON file with the current date in the filename.\n", "    \"\"\"\n", "    # Get the data\n", "    data = get_news_data()\n", "    # Format current date for filename\n", "    date_str = datetime.now().strftime(\"%Y_%m_%d_%H_%M\")\n", "    filename = f\"firecrawl_hacker_news_data_{date_str}.json\"\n", "\n", "    # Save the news items to JSON file\n", "    with open(filename, \"w\") as f:\n", "        json.dump(data[\"extract\"][\"news_items\"], f, indent=4)\n", "\n", "    print(f\"{datetime.now()}: Successfully saved the news data.\")\n", "    \n", "if __name__ == \"__main__\":\n", "    save_firecrawl_news_data()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `save_firecrawl_news_data()` function handles saving the scraped Hacker News data to a JSON file. It first calls `get_news_data()` to fetch the latest data from Hacker News. Then it generates a filename using the current timestamp to ensure uniqueness. The data is saved to a JSON file with that filename, with the news items formatted with proper indentation for readability. Finally, it prints a confirmation message with the current timestamp when the save is complete. This function provides a convenient way to store snapshots of Hacker News data that can be analyzed later.\n", "\n", "Combine these snippets into the `scraper.py` script. Then, we can write a workflow that executes it on schedule:\n", "\n", "```bash\n", "cd ..  # Change back to the project root directory\n", "touch .github/workflows/hacker-news-scraper.py  # Create the workflow file\n", "```\n", "\n", "Here is what the workflow file must look like:\n", "\n", "```yaml\n", "name: Run Hacker News Scraper\n", "\n", "permissions:\n", "  contents: write\n", "\n", "on:\n", "  schedule:\n", "    - cron: \"0 */6 * * *\"\n", "  workflow_dispatch:\n", "\n", "jobs:\n", "  scrape:\n", "    runs-on: ubuntu-latest\n", "    \n", "    steps:\n", "      - uses: actions/checkout@v3\n", "      \n", "      - name: Set up Python\n", "        uses: actions/setup-python@v4\n", "        with:\n", "          python-version: \"3.10\"\n", "          \n", "      - name: Install dependencies\n", "        run: |\n", "          python -m pip install --upgrade pip\n", "          pip install -r hacker-news-scraper/requirements.txt\n", "          \n", "      - name: <PERSON> scraper\n", "        run: python hacker-news-scraper/scraper.py\n", "        \n", "      - name: Commit and push if changes\n", "        run: |\n", "          git config --local user.email \"github-actions[bot]@users.noreply.github.com\"\n", "          git config --local user.name \"github-actions[bot]\"\n", "          git add .\n", "          git commit -m \"Update scraped data\" -a || exit 0\n", "          git push\n", "```\n", "\n", "This workflow runs our Hacker News scraper every 6 hours using GitHub Actions. It sets up Python, installs dependencies, executes the scraper, and automatically commits any new data to the repository. The workflow can also be triggered manually using the `workflow_dispatch` event. One important note about the paths specified in the workflow file is that they must match your repository's directory structure exactly, including the requirements.txt location and the path to your scraper script.\n", "\n", "To enable the workflow, simply push all the changes to GitHub and test it through the UI. The next runs will be automatic.\n", "\n", "```bash\n", "git add .\n", "git commit -m \"Add a scraping workflow\"\n", "git push origin main\n", "```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. Package publishing workflow"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Publishing Python packages to PyPI (Python Package Index) typically involves several steps. First, developers need to prepare their package by creating a proper directory structure, writing setup files, and ensuring all metadata is correct. Then, the package needs to be built into distribution formats - both source distributions (`sdist`) and wheel distributions (`bdist_wheel`). Finally, these distribution files are uploaded to PyPI using tools like `twine`. This process often requires careful version management and proper credentials for the package repository. While this can be done manually, automating it with CI/CD pipelines like GitHub Actions ensures consistency and reduces human error in the release process.\n", "\n", "For example, the following workflow publishes a new version of a package when you create a new release:\n", "\n", "```yaml\n", "name: Publish Python Package\n", "on:\n", "  release:\n", "    types: [created]\n", "\n", "jobs:\n", "  deploy:\n", "    runs-on: ubuntu-latest\n", "    steps:\n", "      - uses: actions/checkout@v3\n", "      \n", "      - name: Set up Python\n", "        uses: actions/setup-python@v4\n", "        with:\n", "          python-version: '3.10'\n", "          \n", "      - name: Install dependencies\n", "        run: |\n", "          python -m pip install --upgrade pip\n", "          pip install build twine\n", "          \n", "      - name: Build package\n", "        run: python -m build\n", "        \n", "      - name: Publish to PyPI\n", "        env:\n", "          TWINE_USERNAME: __token__\n", "          TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}\n", "        run: |\n", "          python -m twine upload dist/*\n", "```\n", "\n", "The workflow automates publishing Python packages to PyPI when GitHub releases are created. \n", "\n", "Required setup steps:\n", "1. Package must have `setup.py` or `pyproject.toml` configured\n", "2. Create PyPI account at `pypi.org`\n", "3. Generate PyPI API token with upload permissions\n", "4. Store token as `PYPI_API_TOKEN` in repository secrets\n", "\n", "The workflow process:\n", "1. Triggers on new GitHub release\n", "2. Checks out code and sets up Python\n", "3. Installs build tools\n", "4. Creates distribution packages\n", "5. Uploads to PyPI using stored token\n", "\n", "The `__token__` username is used with PyPI's token authentication, while the actual token is accessed securely through GitHub secrets."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. Container build and push workflow"]}, {"cell_type": "markdown", "metadata": {}, "source": ["GitHub Actions can also automate building and pushing Docker containers to container registries like Docker Hub or GitHub Container Registry (GHCR). This workflow is useful for maintaining containerized applications and ensuring your latest code changes are packaged into updated container images.\n", "\n", "The process typically involves:\n", "\n", "1. Building a Docker image from your `Dockerfile`\n", "2. Tagging the image with version/metadata\n", "3. Authenticating with the container registry\n", "4. Pushing the tagged image to the registry\n", "\n", "This automation ensures your container images stay in sync with code changes and are readily available for deployment. Here is a sample workflow containing these steps:\n", "\n", "```yaml\n", "name: B<PERSON> and Push Container\n", "on:\n", "  push:\n", "    branches: [main]\n", "    paths:\n", "      - 'Dockerfile'\n", "      - 'src/**'\n", "  workflow_dispatch:\n", "\n", "jobs:\n", "  build:\n", "    runs-on: ubuntu-latest\n", "    steps:\n", "      - uses: actions/checkout@v3\n", "      \n", "      - name: Set up Docker Buildx\n", "        uses: docker/setup-buildx-action@v2\n", "        \n", "      - name: <PERSON><PERSON> to <PERSON><PERSON>\n", "        uses: docker/login-action@v2\n", "        with:\n", "          username: ${{ secrets.DOCKERHUB_USERNAME }}\n", "          password: ${{ secrets.DOCKERHUB_TOKEN }}\n", "          \n", "      - name: Build and push\n", "        uses: docker/build-push-action@v4\n", "        with:\n", "          context: .\n", "          push: true\n", "          tags: |\n", "            user/app:latest\n", "            user/app:${{ github.sha }}\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This workflow introduces a few new GitHub Actions concepts and syntax:\n", "\n", "The `paths` trigger filter ensures the workflow only runs when changes are made to the Dockerfile or files in the `src` directory, preventing unnecessary builds.\n", "\n", "`docker/setup-buildx-action` configures Docker Buildx, which provides enhanced build capabilities including multi-platform builds and build caching.\n", "\n", "`docker/login-action` handles registry authentication. Before using this, you must:\n", "\n", "1. [Create a Docker Hub account](https://app.docker.com/signup)\n", "2. Generate an access token in Docker Hub settings\n", "3. Add `DOCKERHUB_USERNAME` and `DOCKERHUB_TOKEN` as repository secrets in GitHub\n", "\n", "`docker/build-push-action` is a specialized action for building and pushing Docker images. The configuration shows:\n", "- `context: .` (builds from current directory)\n", "- `push: true` (automatically pushes after building)\n", "- `tags:` specifies multiple tags including:\n", "  - `latest:` rolling tag for most recent version\n", "  - `github.sha:` unique tag using commit hash for versioning\n", "\n", "The workflow assumes you have:\n", "- A valid Dockerfile in your repository\n", "- Required application code in `src` directory\n", "- Docker Hub repository permissions\n", "- Properly configured repository secrets\n", "\n", "When this workflow runs successfully, it produces a containerized version of your application that is automatically published to Docker Hub and can be pulled with either the latest tag or specific commit hash."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Throughout this tutorial, we've explored the fundamentals and practical applications of GitHub Actions for Python development. From understanding core concepts like workflows, jobs, and actions, to implementing real-world examples including automated testing, web scraping, package publishing, and container builds, you've gained hands-on experience with this powerful automation platform. We've also covered critical aspects like managing sensitive data through environment variables and secrets, ensuring your automated workflows are both secure and maintainable.\n", "\n", "As you continue your journey with GitHub Actions, remember that automation is an iterative process. Start small with basic workflows, test thoroughly, and gradually add complexity as needed. The examples provided here serve as templates that you can adapt and expand for your specific use cases. For further learning, explore the [GitHub Actions documentation](https://docs.github.com/en/actions), join the [GitHub Community Forum](https://github.community/), and experiment with the vast ecosystem of pre-built actions available in the [GitHub Marketplace](https://github.com/marketplace?type=actions). Whether you're building a personal project or managing enterprise applications, GitHub Actions provides the tools you need to streamline your development workflow and focus on what matters most - writing great code.\n", "\n", "If you want to learn more about Firecrawl, the web scraping API we used today, you can read the following posts:\n", "\n", "- [Guide to Scheduling Web Scrapers in Python](https://www.firecrawl.dev/blog/automated-web-scraping-free-2025)\n", "- [Mastering Firecrawl's Scrape Endpoint](https://www.firecrawl.dev/blog/mastering-firecrawl-scrape-endpoint)\n", "- [Getting Started With Predicted Outputs in OpenAI](https://www.firecrawl.dev/blog/getting-started-with-predicted-outputs-openai)\n", "\n", "Thank you for reading!"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}