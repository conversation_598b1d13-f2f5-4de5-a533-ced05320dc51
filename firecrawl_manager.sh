#!/bin/bash
# 🔥 Firecrawl 本地服務管理腳本
# 用於啟動、停止、重啟和監控 Firecrawl 服務

# 設定顏色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Firecrawl 專案目錄
FIRECRAWL_DIR="$HOME/projects/firecrawl/firecrawl"

# 顯示橫幅
show_banner() {
    echo -e "${CYAN}🔥 Firecrawl 本地服務管理工具${NC}"
    echo -e "${CYAN}================================${NC}"
    echo ""
}

# 檢查 Docker 是否運行
check_docker() {
    if ! docker ps >/dev/null 2>&1; then
        echo -e "${RED}❌ Docker 未運行，正在啟動...${NC}"
        sudo service docker start
        sleep 3
        
        if ! docker ps >/dev/null 2>&1; then
            echo -e "${RED}❌ Docker 啟動失敗，請檢查 Docker 安裝${NC}"
            exit 1
        fi
        echo -e "${GREEN}✅ Docker 已啟動${NC}"
    else
        echo -e "${GREEN}✅ Docker 正在運行${NC}"
    fi
}

# 檢查 Firecrawl 目錄
check_firecrawl_dir() {
    if [ ! -d "$FIRECRAWL_DIR" ]; then
        echo -e "${RED}❌ Firecrawl 目錄不存在: $FIRECRAWL_DIR${NC}"
        echo -e "${YELLOW}請確認 Firecrawl 已正確安裝${NC}"
        exit 1
    fi
    
    if [ ! -f "$FIRECRAWL_DIR/docker-compose.yaml" ]; then
        echo -e "${RED}❌ 找不到 docker-compose.yaml 檔案${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Firecrawl 目錄檢查通過${NC}"
}

# 啟動服務
start_service() {
    echo -e "${BLUE}🚀 啟動 Firecrawl 服務...${NC}"
    
    cd "$FIRECRAWL_DIR" || exit 1
    
    # 啟動服務
    docker compose up -d
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Firecrawl 服務啟動成功${NC}"
        
        # 等待服務就緒
        echo -e "${YELLOW}⏳ 等待服務就緒...${NC}"
        wait_for_service
        
        show_service_info
    else
        echo -e "${RED}❌ Firecrawl 服務啟動失敗${NC}"
        exit 1
    fi
}

# 停止服務
stop_service() {
    echo -e "${BLUE}⏹️ 停止 Firecrawl 服務...${NC}"
    
    cd "$FIRECRAWL_DIR" || exit 1
    
    docker compose down
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Firecrawl 服務已停止${NC}"
    else
        echo -e "${RED}❌ 停止服務時發生錯誤${NC}"
    fi
}

# 重啟服務
restart_service() {
    echo -e "${BLUE}🔄 重啟 Firecrawl 服務...${NC}"
    stop_service
    sleep 3
    start_service
}

# 檢查服務狀態
check_status() {
    echo -e "${BLUE}📊 檢查服務狀態...${NC}"
    
    cd "$FIRECRAWL_DIR" || exit 1
    
    echo -e "${CYAN}Docker 容器狀態:${NC}"
    docker compose ps
    
    echo ""
    echo -e "${CYAN}服務健康檢查:${NC}"
    
    # 檢查 API 是否可訪問
    if curl -s http://localhost:3002/v1/scrape >/dev/null 2>&1; then
        echo -e "${GREEN}✅ API 服務正常${NC}"
    else
        echo -e "${RED}❌ API 服務無法訪問${NC}"
    fi
    
    # 檢查端口
    if netstat -tlnp 2>/dev/null | grep -q ":3002"; then
        echo -e "${GREEN}✅ 端口 3002 已開放${NC}"
    else
        echo -e "${RED}❌ 端口 3002 未開放${NC}"
    fi
}

# 查看日誌
show_logs() {
    echo -e "${BLUE}📋 顯示服務日誌...${NC}"
    
    cd "$FIRECRAWL_DIR" || exit 1
    
    if [ "$1" = "-f" ] || [ "$1" = "--follow" ]; then
        echo -e "${YELLOW}按 Ctrl+C 停止查看日誌${NC}"
        docker compose logs -f
    else
        docker compose logs --tail=50
    fi
}

# 等待服務就緒
wait_for_service() {
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:3002/v1/scrape >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 服務已就緒！${NC}"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo ""
    echo -e "${YELLOW}⚠️ 服務啟動超時，請檢查日誌${NC}"
    return 1
}

# 顯示服務資訊
show_service_info() {
    echo ""
    echo -e "${CYAN}🎉 Firecrawl 服務已啟動！${NC}"
    echo -e "${CYAN}📍 服務資訊:${NC}"
    echo -e "   API 端點: ${GREEN}http://localhost:3002${NC}"
    echo -e "   管理面板: ${GREEN}http://localhost:3002/admin/CHANGEME/queues${NC}"
    echo -e "   Swagger 文檔: ${GREEN}http://localhost:3002/docs${NC}"
    echo ""
    echo -e "${CYAN}🧪 測試命令:${NC}"
    echo -e "   curl -X POST http://localhost:3002/v1/scrape \\"
    echo -e "     -H 'Content-Type: application/json' \\"
    echo -e "     -d '{\"url\": \"https://example.com\", \"formats\": [\"markdown\"]}'"
    echo ""
    echo -e "${CYAN}📚 Python 範例:${NC}"
    echo -e "   cd /mnt/d/firecrawl"
    echo -e "   python3 examples/basic_scraping.py"
}

# 測試服務
test_service() {
    echo -e "${BLUE}🧪 測試 Firecrawl 服務...${NC}"
    
    # 測試基本爬取
    echo -e "${CYAN}測試基本爬取功能...${NC}"
    response=$(curl -s -X POST http://localhost:3002/v1/scrape \
        -H "Content-Type: application/json" \
        -d '{"url": "https://example.com", "formats": ["markdown"]}')
    
    if echo "$response" | grep -q '"success":true'; then
        echo -e "${GREEN}✅ 爬取功能測試通過${NC}"
    else
        echo -e "${RED}❌ 爬取功能測試失敗${NC}"
        echo "回應: $response"
    fi
    
    # 測試網站地圖
    echo -e "${CYAN}測試網站地圖功能...${NC}"
    response=$(curl -s -X POST http://localhost:3002/v1/map \
        -H "Content-Type: application/json" \
        -d '{"url": "https://example.com"}')
    
    if echo "$response" | grep -q '"success":true'; then
        echo -e "${GREEN}✅ 網站地圖功能測試通過${NC}"
    else
        echo -e "${RED}❌ 網站地圖功能測試失敗${NC}"
        echo "回應: $response"
    fi
}

# 清理資源
cleanup() {
    echo -e "${BLUE}🧹 清理 Docker 資源...${NC}"
    
    cd "$FIRECRAWL_DIR" || exit 1
    
    # 停止並刪除容器
    docker compose down --volumes --remove-orphans
    
    # 清理未使用的映像
    docker system prune -f
    
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 顯示幫助
show_help() {
    echo -e "${CYAN}使用方法:${NC}"
    echo "  $0 [命令]"
    echo ""
    echo -e "${CYAN}可用命令:${NC}"
    echo "  start     - 啟動 Firecrawl 服務"
    echo "  stop      - 停止 Firecrawl 服務"
    echo "  restart   - 重啟 Firecrawl 服務"
    echo "  status    - 檢查服務狀態"
    echo "  logs      - 顯示服務日誌"
    echo "  logs -f   - 即時顯示服務日誌"
    echo "  test      - 測試服務功能"
    echo "  cleanup   - 清理 Docker 資源"
    echo "  help      - 顯示此幫助訊息"
    echo ""
    echo -e "${CYAN}範例:${NC}"
    echo "  $0 start          # 啟動服務"
    echo "  $0 logs -f        # 即時查看日誌"
    echo "  $0 test           # 測試服務"
}

# 主函數
main() {
    show_banner
    
    case "$1" in
        start)
            check_docker
            check_firecrawl_dir
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            check_docker
            check_firecrawl_dir
            restart_service
            ;;
        status)
            check_status
            ;;
        logs)
            show_logs "$2"
            ;;
        test)
            test_service
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        "")
            # 預設行為：啟動服務
            check_docker
            check_firecrawl_dir
            start_service
            ;;
        *)
            echo -e "${RED}❌ 未知命令: $1${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 執行主函數
main "$@"
