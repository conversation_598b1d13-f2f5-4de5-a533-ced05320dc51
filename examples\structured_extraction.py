#!/usr/bin/env python3
"""
Firecrawl 結構化數據提取範例
演示如何使用 Firecrawl 提取結構化數據
"""

import json
import os
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field
from firecrawl import FirecrawlApp

def setup_firecrawl():
    """設定 Firecrawl 應用"""
    api_key = os.getenv('FIRECRAWL_API_KEY')
    if api_key:
        return FirecrawlApp(api_key=api_key)
    else:
        return FirecrawlApp(api_url="http://localhost:3002")

# 定義數據結構模型

class Article(BaseModel):
    """文章數據模型"""
    title: str = Field(description="文章標題")
    author: Optional[str] = Field(description="作者名稱")
    publish_date: Optional[str] = Field(description="發布日期")
    summary: Optional[str] = Field(description="文章摘要")
    content: Optional[str] = Field(description="文章內容")
    tags: Optional[List[str]] = Field(description="文章標籤")

class Product(BaseModel):
    """產品數據模型"""
    name: str = Field(description="產品名稱")
    price: str = Field(description="產品價格")
    description: Optional[str] = Field(description="產品描述")
    rating: Optional[str] = Field(description="產品評分")
    availability: str = Field(description="庫存狀態")
    features: Optional[List[str]] = Field(description="產品特色")

class Company(BaseModel):
    """公司資訊數據模型"""
    name: str = Field(description="公司名稱")
    industry: Optional[str] = Field(description="所屬行業")
    founded_year: Optional[str] = Field(description="成立年份")
    headquarters: Optional[str] = Field(description="總部位置")
    description: Optional[str] = Field(description="公司描述")
    website: Optional[str] = Field(description="官方網站")
    employees: Optional[str] = Field(description="員工數量")

class NewsItem(BaseModel):
    """新聞項目數據模型"""
    headline: str = Field(description="新聞標題")
    summary: Optional[str] = Field(description="新聞摘要")
    publish_time: Optional[str] = Field(description="發布時間")
    source: Optional[str] = Field(description="新聞來源")
    category: Optional[str] = Field(description="新聞分類")

def extract_article_data(url):
    """
    從新聞或部落格網站提取文章數據
    """
    print(f"📰 正在提取文章數據: {url}")
    
    app = setup_firecrawl()
    
    try:
        result = app.scrape_url(
            url,
            formats=["extract"],
            extract={"schema": Article.model_json_schema()}
        )
        
        article_data = result.get('extract', {})
        print("✅ 文章數據提取成功!")
        print(f"📄 標題: {article_data.get('title', 'N/A')}")
        print(f"✍️ 作者: {article_data.get('author', 'N/A')}")
        print(f"📅 發布日期: {article_data.get('publish_date', 'N/A')}")
        
        return article_data
        
    except Exception as e:
        print(f"❌ 文章數據提取失敗: {str(e)}")
        return None

def extract_product_data(url):
    """
    從電商網站提取產品數據
    """
    print(f"🛍️ 正在提取產品數據: {url}")
    
    app = setup_firecrawl()
    
    try:
        result = app.scrape_url(
            url,
            formats=["extract"],
            extract={"schema": Product.model_json_schema()}
        )
        
        product_data = result.get('extract', {})
        print("✅ 產品數據提取成功!")
        print(f"🏷️ 產品名稱: {product_data.get('name', 'N/A')}")
        print(f"💰 價格: {product_data.get('price', 'N/A')}")
        print(f"📦 庫存狀態: {product_data.get('availability', 'N/A')}")
        
        return product_data
        
    except Exception as e:
        print(f"❌ 產品數據提取失敗: {str(e)}")
        return None

def extract_company_data(url):
    """
    從公司官網提取公司資訊
    """
    print(f"🏢 正在提取公司資訊: {url}")
    
    app = setup_firecrawl()
    
    try:
        result = app.scrape_url(
            url,
            formats=["extract"],
            extract={"schema": Company.model_json_schema()}
        )
        
        company_data = result.get('extract', {})
        print("✅ 公司資訊提取成功!")
        print(f"🏢 公司名稱: {company_data.get('name', 'N/A')}")
        print(f"🏭 行業: {company_data.get('industry', 'N/A')}")
        print(f"📍 總部: {company_data.get('headquarters', 'N/A')}")
        
        return company_data
        
    except Exception as e:
        print(f"❌ 公司資訊提取失敗: {str(e)}")
        return None

def extract_with_prompt(url, prompt):
    """
    使用自然語言提示提取數據（無需預定義結構）
    """
    print(f"🤖 使用 AI 提示提取數據: {url}")
    print(f"💭 提示: {prompt}")
    
    app = setup_firecrawl()
    
    try:
        result = app.scrape_url(
            url,
            formats=["extract"],
            extract={"prompt": prompt}
        )
        
        extracted_data = result.get('extract', {})
        print("✅ AI 數據提取成功!")
        print("📊 提取的數據:")
        print(json.dumps(extracted_data, ensure_ascii=False, indent=2))
        
        return extracted_data
        
    except Exception as e:
        print(f"❌ AI 數據提取失敗: {str(e)}")
        return None

def batch_extract_news(urls):
    """
    批量提取新聞數據
    """
    print(f"📰 正在批量提取 {len(urls)} 個新聞頁面的數據")
    
    app = setup_firecrawl()
    all_news = []
    
    for i, url in enumerate(urls, 1):
        print(f"處理第 {i}/{len(urls)} 個: {url}")
        
        try:
            result = app.scrape_url(
                url,
                formats=["extract"],
                extract={"schema": NewsItem.model_json_schema()}
            )
            
            news_data = result.get('extract', {})
            if news_data:
                all_news.append(news_data)
                print(f"  ✅ 成功: {news_data.get('headline', 'N/A')[:50]}...")
            
        except Exception as e:
            print(f"  ❌ 失敗: {str(e)}")
    
    print(f"🎉 批量提取完成! 成功提取 {len(all_news)} 條新聞")
    return all_news

def save_extracted_data(data, filename_prefix):
    """儲存提取的數據"""
    if not data:
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{filename_prefix}_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"💾 數據已儲存到: {filename}")

def main():
    """
    主函數 - 演示結構化數據提取
    """
    print("🔥 Firecrawl 結構化數據提取範例")
    print("=" * 50)
    
    # 範例 URLs（請替換為實際的網站）
    article_url = "https://example-blog.com/article"
    product_url = "https://example-shop.com/product"
    company_url = "https://example-company.com/about"
    
    # 1. 文章數據提取
    print("\n1️⃣ 文章數據提取範例")
    print("-" * 30)
    article_data = extract_article_data(article_url)
    if article_data:
        save_extracted_data(article_data, "article_data")
    
    # 2. 產品數據提取
    print("\n2️⃣ 產品數據提取範例")
    print("-" * 30)
    product_data = extract_product_data(product_url)
    if product_data:
        save_extracted_data(product_data, "product_data")
    
    # 3. 公司資訊提取
    print("\n3️⃣ 公司資訊提取範例")
    print("-" * 30)
    company_data = extract_company_data(company_url)
    if company_data:
        save_extracted_data(company_data, "company_data")
    
    # 4. 使用自然語言提示提取
    print("\n4️⃣ AI 提示提取範例")
    print("-" * 30)
    prompt = "提取這個網頁的主要聯絡資訊，包括電話、郵箱、地址等"
    ai_data = extract_with_prompt(company_url, prompt)
    if ai_data:
        save_extracted_data(ai_data, "ai_extracted_data")
    
    # 5. 批量新聞提取
    print("\n5️⃣ 批量新聞提取範例")
    print("-" * 30)
    news_urls = [
        "https://example-news.com/article1",
        "https://example-news.com/article2",
        "https://example-news.com/article3"
    ]
    batch_news = batch_extract_news(news_urls)
    if batch_news:
        save_extracted_data(batch_news, "batch_news_data")
    
    print("\n🎉 所有結構化數據提取範例執行完成!")

if __name__ == "__main__":
    main()
