# 🔥 Firecrawl 簡化啟動腳本
param(
    [Parameter(Position=0)]
    [string]$Action = "start"
)

Write-Host "🔥 Firecrawl 服務管理工具" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host ""

# 檢查 WSL 狀態
Write-Host "📋 檢查 WSL 狀態..." -ForegroundColor Blue
try {
    $wslStatus = wsl --list --verbose 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ WSL 未安裝或無法訪問" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ WSL 可用" -ForegroundColor Green
}
catch {
    Write-Host "❌ WSL 檢查失敗: $_" -ForegroundColor Red
    exit 1
}

# 根據動作執行相應命令
switch ($Action.ToLower()) {
    "start" {
        Write-Host "🚀 啟動 Firecrawl 服務..." -ForegroundColor Blue
        
        # 啟動 Docker 服務
        Write-Host "   啟動 Docker 服務..." -ForegroundColor Yellow
        wsl -d Ubuntu -e bash -c 'sudo service docker start'
        
        # 進入目錄並啟動服務
        Write-Host "   啟動 Firecrawl 容器..." -ForegroundColor Yellow
        wsl -d Ubuntu -e bash -c 'cd ~/projects/firecrawl/firecrawl && docker compose up -d'
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Firecrawl 服務啟動成功" -ForegroundColor Green
            
            # 等待服務就緒
            Write-Host "⏳ 等待服務就緒..." -ForegroundColor Yellow
            Start-Sleep -Seconds 10
            
            # 顯示服務資訊
            Write-Host ""
            Write-Host "🎉 Firecrawl 服務已啟動！" -ForegroundColor Green
            Write-Host ""
            Write-Host "📍 服務資訊:" -ForegroundColor Cyan
            Write-Host "   API 端點: http://localhost:3002" -ForegroundColor White
            Write-Host "   管理面板: http://localhost:3002/admin/CHANGEME/queues" -ForegroundColor White
            Write-Host ""
            Write-Host "🧪 測試命令:" -ForegroundColor Cyan
            Write-Host "   .\start_firecrawl.ps1 test" -ForegroundColor White
            Write-Host ""
            Write-Host "📚 執行 Python 範例:" -ForegroundColor Cyan
            Write-Host "   python examples/basic_scraping.py" -ForegroundColor White
        }
        else {
            Write-Host "❌ Firecrawl 服務啟動失敗" -ForegroundColor Red
        }
    }
    
    "stop" {
        Write-Host "⏹️ 停止 Firecrawl 服務..." -ForegroundColor Blue
        wsl -d Ubuntu -e bash -c 'cd ~/projects/firecrawl/firecrawl && docker compose down'
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Firecrawl 服務已停止" -ForegroundColor Green
        }
        else {
            Write-Host "❌ 停止服務時發生錯誤" -ForegroundColor Red
        }
    }
    
    "status" {
        Write-Host "📊 檢查服務狀態..." -ForegroundColor Blue
        Write-Host ""
        Write-Host "Docker 容器狀態:" -ForegroundColor Cyan
        wsl -d Ubuntu -e bash -c 'cd ~/projects/firecrawl/firecrawl && docker compose ps'
        
        Write-Host ""
        Write-Host "測試 API 連接:" -ForegroundColor Cyan
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:3002/v1/scrape" -Method Post -ContentType "application/json" -Body '{"url": "https://example.com", "formats": ["markdown"]}' -TimeoutSec 10
            if ($response.success) {
                Write-Host "✅ API 服務正常" -ForegroundColor Green
            }
            else {
                Write-Host "❌ API 服務異常" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "❌ 無法連接到 API 服務" -ForegroundColor Red
        }
    }
    
    "test" {
        Write-Host "🧪 測試 Firecrawl 服務..." -ForegroundColor Blue
        
        try {
            # 測試基本爬取
            Write-Host "   測試基本爬取功能..." -ForegroundColor Yellow
            $response = Invoke-RestMethod -Uri "http://localhost:3002/v1/scrape" -Method Post -ContentType "application/json" -Body '{"url": "https://example.com", "formats": ["markdown"]}' -TimeoutSec 30
            
            if ($response.success) {
                Write-Host "✅ 爬取功能測試通過" -ForegroundColor Green
                Write-Host "   標題: $($response.data.metadata.title)" -ForegroundColor White
                Write-Host "   內容長度: $($response.data.markdown.Length)" -ForegroundColor White
            }
            else {
                Write-Host "❌ 爬取功能測試失敗" -ForegroundColor Red
            }
            
            # 測試網站地圖
            Write-Host "   測試網站地圖功能..." -ForegroundColor Yellow
            $mapResponse = Invoke-RestMethod -Uri "http://localhost:3002/v1/map" -Method Post -ContentType "application/json" -Body '{"url": "https://example.com"}' -TimeoutSec 30
            
            if ($mapResponse.success) {
                Write-Host "✅ 網站地圖功能測試通過" -ForegroundColor Green
                Write-Host "   找到 $($mapResponse.links.Count) 個連結" -ForegroundColor White
            }
            else {
                Write-Host "❌ 網站地圖功能測試失敗" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "❌ 測試失敗: $_" -ForegroundColor Red
        }
    }
    
    "logs" {
        Write-Host "📋 顯示服務日誌..." -ForegroundColor Blue
        wsl -d Ubuntu -e bash -c 'cd ~/projects/firecrawl/firecrawl && docker compose logs --tail=50'
    }
    
    "restart" {
        Write-Host "🔄 重啟 Firecrawl 服務..." -ForegroundColor Blue
        
        # 停止服務
        wsl -d Ubuntu -e bash -c 'cd ~/projects/firecrawl/firecrawl && docker compose down'
        Start-Sleep -Seconds 3
        
        # 啟動服務
        wsl -d Ubuntu -e bash -c 'sudo service docker start'
        wsl -d Ubuntu -e bash -c 'cd ~/projects/firecrawl/firecrawl && docker compose up -d'
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Firecrawl 服務重啟成功" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Firecrawl 服務重啟失敗" -ForegroundColor Red
        }
    }
    
    "help" {
        Write-Host "📖 使用方法:" -ForegroundColor Cyan
        Write-Host "   .\start_firecrawl.ps1 [命令]" -ForegroundColor White
        Write-Host ""
        Write-Host "🔧 可用命令:" -ForegroundColor Cyan
        Write-Host "   start     - 啟動 Firecrawl 服務 (預設)" -ForegroundColor White
        Write-Host "   stop      - 停止 Firecrawl 服務" -ForegroundColor White
        Write-Host "   restart   - 重啟 Firecrawl 服務" -ForegroundColor White
        Write-Host "   status    - 檢查服務狀態" -ForegroundColor White
        Write-Host "   logs      - 顯示服務日誌" -ForegroundColor White
        Write-Host "   test      - 測試服務功能" -ForegroundColor White
        Write-Host "   help      - 顯示此幫助訊息" -ForegroundColor White
        Write-Host ""
        Write-Host "💡 範例:" -ForegroundColor Cyan
        Write-Host "   .\start_firecrawl.ps1 start" -ForegroundColor White
        Write-Host "   .\start_firecrawl.ps1 status" -ForegroundColor White
        Write-Host "   .\start_firecrawl.ps1 test" -ForegroundColor White
    }
    
    default {
        Write-Host "❌ 未知命令: $Action" -ForegroundColor Red
        Write-Host ""
        Write-Host "使用 '.\start_firecrawl.ps1 help' 查看可用命令" -ForegroundColor Yellow
    }
}

Write-Host ""
