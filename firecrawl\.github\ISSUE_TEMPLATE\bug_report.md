---
name: Bug report
about: Create a report to help us improve
title: "[Bug] "
labels: bug
assignees: ''

---

**Describe the Bug**
Provide a clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the issue:
1. Configure the environment or settings with '...'
2. Run the command '...'
3. Observe the error or unexpected output at '...'
4. Log output/error message

**Expected Behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots or copies of the command line output to help explain the issue.

**Environment (please complete the following information):**
- OS: [e.g. macOS, Linux, Windows]
- Deployment Type: [Cloud (firecrawl.dev) / Self-hosted]
- Firecrawl Version: [e.g. 1.2.3]
- Node.js Version: [e.g. 14.x]

**Logs**
If applicable, include detailed logs to help understand the problem.

**Additional Context**
Add any other context about the problem here, such as configuration specifics, network conditions, data volumes, etc.
