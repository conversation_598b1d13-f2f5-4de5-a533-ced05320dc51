# 🔥 Firecrawl 使用指南

## 什麼是 Firecrawl？

Firecrawl 是一個強大的網頁爬取和數據提取 API 服務，能夠：
- 將任何網站轉換為乾淨的 Markdown 格式
- 提取結構化數據
- 爬取整個網站的所有可訪問頁面
- 搜索網頁並獲取完整內容
- 支援 JavaScript 渲染的動態內容
- 繞過反爬蟲機制

## 🚀 快速開始

### 1. 雲端服務使用（推薦）

#### 獲取 API Key
1. 訪問 [firecrawl.dev](https://firecrawl.dev)
2. 註冊帳號並獲取 API Key

#### Python SDK 安裝
```bash
pip install firecrawl-py
```

#### Node.js SDK 安裝
```bash
npm install @mendable/firecrawl-js
```

### 2. 自主部署（本地運行）

#### 前置需求
- Docker 和 Docker Compose
- Git

#### 部署步驟
```bash
# 1. Clone 專案（已完成）
cd firecrawl

# 2. 建立環境變數檔案
cp .env.example .env

# 3. 編輯 .env 檔案（可選）
# 基本配置已足夠，如需 AI 功能請添加 OPENAI_API_KEY

# 4. 建置並啟動服務
docker compose build
docker compose up
```

服務將在 `http://localhost:3002` 啟動

## 📖 主要功能

### 1. Scrape - 單頁面爬取
提取單個網頁的內容，支援多種格式輸出。

**API 呼叫範例：**
```bash
curl -X POST http://localhost:3002/v1/scrape \
    -H 'Content-Type: application/json' \
    -d '{
      "url": "https://example.com",
      "formats": ["markdown", "html"]
    }'
```

**Python 範例：**
```python
from firecrawl import FirecrawlApp

app = FirecrawlApp()  # 本地部署不需要 API key
result = app.scrape_url(
    'https://example.com',
    formats=["markdown", "html"]
)
print(result)
```

### 2. Crawl - 網站爬取
爬取整個網站的所有可訪問頁面。

**API 呼叫範例：**
```bash
curl -X POST http://localhost:3002/v1/crawl \
    -H 'Content-Type: application/json' \
    -d '{
      "url": "https://example.com",
      "limit": 10,
      "scrapeOptions": {
        "formats": ["markdown"]
      }
    }'
```

### 3. Map - 網站地圖
快速獲取網站的所有 URL 連結。

```bash
curl -X POST http://localhost:3002/v1/map \
    -H 'Content-Type: application/json' \
    -d '{
      "url": "https://example.com"
    }'
```

### 4. Search - 網頁搜索
搜索網頁並獲取完整內容。

```bash
curl -X POST http://localhost:3002/v1/search \
    -H 'Content-Type: application/json' \
    -d '{
      "query": "firecrawl tutorial",
      "limit": 5
    }'
```

### 5. Extract - 結構化數據提取
使用 AI 從網頁中提取結構化數據。

```python
from firecrawl import FirecrawlApp
from pydantic import BaseModel
from typing import List

class Article(BaseModel):
    title: str
    author: str
    publish_date: str
    summary: str

app = FirecrawlApp()
result = app.scrape_url(
    'https://news.example.com',
    formats=["extract"],
    extract={"schema": Article.model_json_schema()}
)
```

## 🛠️ 實用範例

### 範例 1：新聞網站爬取
```python
import json
from firecrawl import FirecrawlApp
from datetime import datetime

def scrape_news_site():
    app = FirecrawlApp()
    
    # 爬取新聞網站
    result = app.crawl_url(
        'https://news.example.com',
        limit=20,
        scrape_options={
            'formats': ['markdown'],
            'onlyMainContent': True
        }
    )
    
    # 儲存結果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"news_data_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"已儲存 {len(result.get('data', []))} 篇文章到 {filename}")

if __name__ == "__main__":
    scrape_news_site()
```

### 範例 2：電商產品資訊提取
```python
from firecrawl import FirecrawlApp
from pydantic import BaseModel
from typing import List, Optional

class Product(BaseModel):
    name: str
    price: str
    description: Optional[str]
    rating: Optional[str]
    availability: str

def extract_product_info(url):
    app = FirecrawlApp()
    
    result = app.scrape_url(
        url,
        formats=["extract"],
        extract={"schema": Product.model_json_schema()}
    )
    
    return result['extract']

# 使用範例
product_url = "https://shop.example.com/product/123"
product_info = extract_product_info(product_url)
print(f"產品名稱: {product_info['name']}")
print(f"價格: {product_info['price']}")
```

## 🔧 進階配置

### 環境變數配置
在 `.env` 檔案中可以配置：

```env
# 基本設定
PORT=3002
HOST=0.0.0.0

# AI 功能（需要 OpenAI API Key）
OPENAI_API_KEY=your_openai_api_key

# 代理設定
PROXY_SERVER=http://proxy.example.com:8080
PROXY_USERNAME=username
PROXY_PASSWORD=password

# 系統資源限制
MAX_CPU=0.8
MAX_RAM=0.8
```

### 自訂爬取選項
```python
scrape_options = {
    'formats': ['markdown', 'html'],
    'onlyMainContent': True,  # 只提取主要內容
    'excludeTags': ['nav', 'footer', 'aside'],  # 排除特定標籤
    'waitFor': 3000,  # 等待動態內容載入（毫秒）
}
```

## 📊 監控和管理

### Bull Queue 管理介面
訪問 `http://localhost:3002/admin/CHANGEME/queues` 查看爬取任務狀態。

### 日誌查看
```bash
# 查看所有服務日誌
docker compose logs

# 查看特定服務日誌
docker compose logs api
docker compose logs playwright-service
```

## 🚨 常見問題

### 1. Docker 容器啟動失敗
```bash
# 檢查容器狀態
docker compose ps

# 查看錯誤日誌
docker compose logs [service_name]
```

### 2. API 請求超時
- 增加 `waitFor` 參數值
- 檢查網路連線
- 確認目標網站可訪問

### 3. 記憶體不足
- 調整 `MAX_RAM` 環境變數
- 減少並發爬取數量
- 增加系統記憶體

## 🎯 最佳實踐

1. **尊重網站政策**：遵守 robots.txt 和網站使用條款
2. **合理設定限制**：避免過度爬取造成服務器負擔
3. **錯誤處理**：實作重試機制和錯誤處理
4. **數據清理**：對提取的數據進行驗證和清理
5. **定期更新**：保持 Firecrawl 版本更新

## 📚 更多資源

- [官方文檔](https://docs.firecrawl.dev)
- [GitHub 專案](https://github.com/mendableai/firecrawl)
- [Discord 社群](https://discord.com/invite/gSmWdAkdwd)
- [範例專案](https://github.com/mendableai/firecrawl/tree/main/examples)

## 📁 專案結構

```
firecrawl/                    # 主要專案目錄
├── README.md                 # 專案說明
├── SELF_HOST.md             # 自主部署指南
├── docker-compose.yaml      # Docker 編排檔案
├── .env                     # 環境變數設定
└── apps/                    # 應用程式目錄
    ├── api/                 # API 服務
    ├── playwright-service-ts/ # 瀏覽器服務
    └── ...

examples/                     # 使用範例
├── basic_scraping.py        # 基本爬取範例
└── structured_extraction.py # 結構化數據提取範例

Firecrawl_使用指南.md        # 中文使用指南
requirements.txt             # Python 依賴套件
setup.py                     # 環境設定腳本
start_firecrawl.py          # 服務啟動腳本
test_firecrawl.py           # 測試腳本
```

## 🚀 快速啟動流程

### 方法一：使用啟動腳本（推薦）
```bash
# 1. 安裝 Python 依賴
pip install -r requirements.txt

# 2. 設定環境並啟動服務
python setup.py

# 3. 啟動 Firecrawl 服務
python start_firecrawl.py start

# 4. 測試服務
python test_firecrawl.py

# 5. 執行範例
python examples/basic_scraping.py
```

### 方法二：手動啟動
```bash
# 1. 進入 firecrawl 目錄
cd firecrawl

# 2. 建立 .env 檔案（如果不存在）
cp .env.example .env

# 3. 建置並啟動服務
docker compose build
docker compose up -d

# 4. 測試服務
curl http://localhost:3002/health
```

## 🛠️ 服務管理命令

使用 `start_firecrawl.py` 腳本管理服務：

```bash
# 啟動服務
python start_firecrawl.py start

# 停止服務
python start_firecrawl.py stop

# 重啟服務
python start_firecrawl.py restart

# 查看狀態
python start_firecrawl.py status

# 查看日誌
python start_firecrawl.py logs

# 即時查看日誌
python start_firecrawl.py logs -f

# 重新建置
python start_firecrawl.py build
```

---

**注意事項**：使用 Firecrawl 時請遵守相關法律法規和網站使用條款，負責任地進行網頁爬取。
