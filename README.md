# 🔥 Firecrawl 本地部署與使用

這是一個完整的 Firecrawl 本地部署方案，包含服務管理工具和實用範例。

## 🚀 快速開始

### 1. 啟動本地服務

**Windows (PowerShell)**:
```powershell
.\start_firecrawl.ps1 start
```

**Linux/WSL**:
```bash
./firecrawl_manager.sh start
```

### 2. 測試服務
```bash
# 測試 API 功能
.\start_firecrawl.ps1 test  # Windows
./firecrawl_manager.sh test # Linux/WSL
```

### 3. 執行範例
```bash
# 安裝 Python 依賴
pip install -r requirements.txt

# 執行基本爬取範例
python examples/basic_scraping.py

# 執行結構化數據提取範例
python examples/structured_extraction.py
```

## 📊 服務管理

### 常用命令
```bash
# Windows PowerShell
.\start_firecrawl.ps1 start     # 啟動服務
.\start_firecrawl.ps1 stop      # 停止服務
.\start_firecrawl.ps1 status    # 檢查狀態
.\start_firecrawl.ps1 logs      # 查看日誌

# Linux/WSL
./firecrawl_manager.sh start    # 啟動服務
./firecrawl_manager.sh stop     # 停止服務
./firecrawl_manager.sh status   # 檢查狀態
./firecrawl_manager.sh logs     # 查看日誌
```

### 服務資訊
- **API 端點**: http://localhost:3002
- **管理面板**: http://localhost:3002/admin/CHANGEME/queues
- **API 文檔**: http://localhost:3002/docs

## 🔧 在其他專案中使用 Firecrawl

### 方法 1：使用本地服務

1. **確保本地服務運行**：
   ```bash
   .\start_firecrawl.ps1 start  # 啟動本地服務
   ```

2. **在新專案中安裝套件**：
   ```bash
   pip install firecrawl-py pydantic requests
   ```

3. **使用範例**：
   ```python
   from firecrawl import FirecrawlApp

   # 連接本地服務
   app = FirecrawlApp(api_url="http://localhost:3002")

   # 爬取網頁
   result = app.scrape_url("https://example.com")
   print(result['markdown'])
   ```

### 方法 2：使用雲端服務

1. **註冊 Firecrawl 雲端服務**：
   - 訪問 [firecrawl.dev](https://firecrawl.dev)
   - 獲取 API Key

2. **在專案中使用**：
   ```python
   from firecrawl import FirecrawlApp

   # 使用雲端服務
   app = FirecrawlApp(api_key="fc-your_api_key")

   # 爬取網頁
   result = app.scrape_url("https://example.com")
   print(result['markdown'])
   ```

### 快速整合模板

複製 `examples/` 目錄到你的新專案，然後修改其中的範例程式即可快速開始。

## 📚 基本使用範例

### 網頁爬取
```python
from firecrawl import FirecrawlApp

# 連接本地服務
app = FirecrawlApp(api_url="http://localhost:3002")

# 爬取單頁
result = app.scrape_url("https://example.com")
print(result['markdown'])

# 爬取網站
crawl_result = app.crawl_url("https://example.com", limit=5)
```

### 結構化數據提取
```python
from pydantic import BaseModel

class Article(BaseModel):
    title: str
    author: str
    summary: str

# 提取結構化數據
result = app.scrape_url(
    "https://news.example.com",
    formats=["extract"],
    extract={"schema": Article.model_json_schema()}
)
```

## 📁 專案結構

```
firecrawl/
├── README.md                    # 本檔案
├── Firecrawl_使用指南.md        # 詳細使用指南
├── requirements.txt             # Python 依賴套件
├── start_firecrawl.ps1          # Windows 服務管理腳本
├── firecrawl_manager.sh         # Linux/WSL 服務管理腳本
├── examples/                    # 使用範例
│   ├── basic_scraping.py        # 基本爬取範例
│   └── structured_extraction.py # 結構化數據提取範例
└── firecrawl/                   # Firecrawl 服務本體
    ├── docker-compose.yaml     # Docker 編排檔案
    └── apps/                    # 應用程式目錄
```

## 🎓 學習資源

- **詳細指南**：[Firecrawl_使用指南.md](./Firecrawl_使用指南.md)
- **官方文檔**：https://docs.firecrawl.dev
- **GitHub 專案**：https://github.com/mendableai/firecrawl

---

**開始你的 Firecrawl 之旅吧！** 🚀
