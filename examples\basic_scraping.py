#!/usr/bin/env python3
"""
Firecrawl 基本爬取範例
演示如何使用 Firecrawl 進行基本的網頁爬取操作
"""

import json
import os
from datetime import datetime
from firecrawl import FirecrawlApp

def setup_firecrawl():
    """
    設定 Firecrawl 應用
    如果是本地部署，不需要 API key
    如果使用雲端服務，需要設定 FIRECRAWL_API_KEY 環境變數
    """
    api_key = os.getenv('FIRECRAWL_API_KEY')
    if api_key:
        return FirecrawlApp(api_key=api_key)
    else:
        # 本地部署，使用預設設定
        return FirecrawlApp(api_url="http://localhost:3002")

def scrape_single_page(url):
    """
    爬取單個網頁
    """
    print(f"🔍 正在爬取: {url}")
    
    app = setup_firecrawl()
    
    try:
        result = app.scrape_url(
            url,
            formats=["markdown", "html"],
            # 可選參數
            # onlyMainContent=True,  # 只提取主要內容
            # excludeTags=['nav', 'footer', 'aside'],  # 排除特定標籤
            # waitFor=3000,  # 等待動態內容載入
        )
        
        print("✅ 爬取成功!")
        print(f"📄 標題: {result.get('metadata', {}).get('title', 'N/A')}")
        print(f"📝 內容長度: {len(result.get('markdown', ''))}")
        
        return result
        
    except Exception as e:
        print(f"❌ 爬取失敗: {str(e)}")
        return None

def crawl_website(url, limit=10):
    """
    爬取整個網站
    """
    print(f"🕷️ 正在爬取網站: {url} (限制: {limit} 頁)")
    
    app = setup_firecrawl()
    
    try:
        result = app.crawl_url(
            url,
            limit=limit,
            scrape_options={
                'formats': ['markdown'],
                'onlyMainContent': True,
            }
        )
        
        print("✅ 網站爬取成功!")
        pages = result.get('data', [])
        print(f"📚 總共爬取了 {len(pages)} 個頁面")
        
        return result
        
    except Exception as e:
        print(f"❌ 網站爬取失敗: {str(e)}")
        return None

def map_website(url):
    """
    獲取網站地圖（所有連結）
    """
    print(f"🗺️ 正在獲取網站地圖: {url}")
    
    app = setup_firecrawl()
    
    try:
        result = app.map_url(url)
        
        print("✅ 網站地圖獲取成功!")
        links = result.get('links', [])
        print(f"🔗 找到 {len(links)} 個連結")
        
        # 顯示前 10 個連結
        for i, link in enumerate(links[:10], 1):
            print(f"  {i}. {link}")
        
        if len(links) > 10:
            print(f"  ... 還有 {len(links) - 10} 個連結")
        
        return result
        
    except Exception as e:
        print(f"❌ 網站地圖獲取失敗: {str(e)}")
        return None

def search_web(query, limit=5):
    """
    搜索網頁
    """
    print(f"🔍 正在搜索: {query}")
    
    app = setup_firecrawl()
    
    try:
        result = app.search(
            query,
            limit=limit,
            # 可選：同時爬取搜索結果
            # scrape_options={
            #     'formats': ['markdown']
            # }
        )
        
        print("✅ 搜索成功!")
        results = result.get('data', [])
        print(f"📋 找到 {len(results)} 個結果")
        
        for i, item in enumerate(results, 1):
            print(f"  {i}. {item.get('title', 'N/A')}")
            print(f"     URL: {item.get('url', 'N/A')}")
            print(f"     描述: {item.get('description', 'N/A')[:100]}...")
            print()
        
        return result
        
    except Exception as e:
        print(f"❌ 搜索失敗: {str(e)}")
        return None

def save_result(data, filename_prefix):
    """
    儲存結果到 JSON 檔案
    """
    if not data:
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{filename_prefix}_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"💾 結果已儲存到: {filename}")

def main():
    """
    主函數 - 演示各種功能
    """
    print("🔥 Firecrawl 基本爬取範例")
    print("=" * 50)
    
    # 範例網站
    test_url = "https://example.com"
    
    # 1. 單頁爬取
    print("\n1️⃣ 單頁爬取範例")
    print("-" * 30)
    scrape_result = scrape_single_page(test_url)
    if scrape_result:
        save_result(scrape_result, "scrape_result")
    
    # 2. 網站地圖
    print("\n2️⃣ 網站地圖範例")
    print("-" * 30)
    map_result = map_website(test_url)
    if map_result:
        save_result(map_result, "map_result")
    
    # 3. 網站爬取（小規模）
    print("\n3️⃣ 網站爬取範例")
    print("-" * 30)
    crawl_result = crawl_website(test_url, limit=5)
    if crawl_result:
        save_result(crawl_result, "crawl_result")
    
    # 4. 網頁搜索
    print("\n4️⃣ 網頁搜索範例")
    print("-" * 30)
    search_result = search_web("firecrawl tutorial", limit=3)
    if search_result:
        save_result(search_result, "search_result")
    
    print("\n🎉 所有範例執行完成!")

if __name__ == "__main__":
    main()
